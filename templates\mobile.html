<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="ParadoxGPT">
    <meta name="theme-color" content="#000000">
    <title>ParadoxGPT - Mobile</title>
    <meta name="description" content="ParadoxGPT - AI Code Generation for Mobile">
    
    <!-- Mobile-optimized CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Fallback styles in case CSS doesn't load -->
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #ffffff;
            color: #000000;
        }
        .mobile-app {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .mobile-header {
            background: #ffffff;
            border-bottom: 1px solid #e0e0e0;
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .app-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0;
        }
        .mobile-main {
            flex: 1;
            padding: 1rem;
        }
        .welcome-mobile {
            text-align: center;
            padding: 2rem 1rem;
        }
        .welcome-mobile h2 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
        .mobile-input-area {
            padding: 1rem;
            border-top: 1px solid #e0e0e0;
        }
        .input-container-mobile {
            display: flex;
            gap: 0.5rem;
            background: #f5f5f5;
            border-radius: 24px;
            padding: 0.5rem;
        }
        .message-input-mobile {
            flex: 1;
            border: none;
            background: none;
            padding: 0.5rem;
            font-size: 1rem;
            outline: none;
        }
        .send-btn-mobile {
            background: #000000;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
        }
    </style>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAnalytics } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js';

        const firebaseConfig = {
            apiKey: "{{ firebase_config.api_key | default('your-api-key-here') }}",
            authDomain: "{{ firebase_config.auth_domain | default('your-project-id.firebaseapp.com') }}",
            projectId: "{{ firebase_config.project_id | default('your-project-id') }}",
            storageBucket: "{{ firebase_config.storage_bucket | default('your-project-id.appspot.com') }}",
            messagingSenderId: "{{ firebase_config.messaging_sender_id | default('your-sender-id') }}",
            appId: "{{ firebase_config.app_id | default('your-app-id') }}",
            measurementId: "{{ firebase_config.measurement_id | default('') }}"
        };

        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        window.firebaseApp = app;
        window.firebaseAuth = auth;
        window.firebaseDb = db;
        window.firebaseReady = true;
        window.dispatchEvent(new CustomEvent('firebaseReady'));

        try {
            window.firebaseAnalytics = getAnalytics(app);
        } catch (error) {
            console.log('Analytics not available:', error);
        }
    </script>
</head>
<body>
    <!-- Debug: Visible test content -->
    <div style="position: fixed; top: 0; left: 0; background: red; color: white; padding: 10px; z-index: 9999; font-size: 12px;">
        MOBILE TEMPLATE LOADED - If you see this, HTML is working
    </div>

    <!-- Mobile App Container -->
    <div class="mobile-app">
        <!-- Mobile Header -->
        <header class="mobile-header">
            <button class="menu-btn" id="menuBtn" aria-label="Menu">
                <i class="fas fa-bars"></i>
            </button>
            <h1 class="app-title">ParadoxGPT</h1>
            <button class="user-btn" id="userBtn" aria-label="User menu">
                <i class="fas fa-user"></i>
            </button>
        </header>

        <!-- Mobile Sidebar -->
        <aside class="mobile-sidebar" id="mobileSidebar">
            <div class="sidebar-header">
                <h2>Chat History</h2>
                <button class="close-sidebar" id="closeSidebar" aria-label="Close menu">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <button class="new-chat-mobile" id="newChatMobile">
                <i class="fas fa-plus"></i>
                New Chat
            </button>
            
            <div class="chat-history-mobile" id="chatHistoryMobile">
                <!-- Chat history will be populated here -->
            </div>
            
            <div class="sidebar-footer-mobile">
                <!-- User profile for mobile -->
                <div id="userProfileMobile" class="user-profile-mobile" style="display: none;">
                    <div class="user-avatar-mobile" id="userAvatarMobile"></div>
                    <div class="user-info-mobile">
                        <p class="user-name-mobile" id="userNameMobile"></p>
                        <p class="user-email-mobile" id="userEmailMobile"></p>
                    </div>
                    <button id="logoutBtnMobile" class="logout-btn-mobile">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </div>
                
                <!-- Login section for mobile -->
                <div id="loginSectionMobile" class="login-section-mobile">
                    <button id="loginBtnMobile" class="login-btn-mobile">
                        <i class="fas fa-sign-in-alt"></i>
                        Sign In to Save Chats
                    </button>
                </div>
            </div>
        </aside>

        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>

        <!-- Main Chat Area -->
        <main class="mobile-main">
            <div class="chat-container-mobile" id="chatContainerMobile">
                <!-- Welcome Screen -->
                <div class="welcome-mobile" id="welcomeMobile">
                    <div class="welcome-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h2>Welcome to ParadoxGPT</h2>
                    <p>Your AI coding assistant</p>
                    <div class="quick-actions">
                        <button class="quick-action" data-prompt="Help me write a Python function">
                            <i class="fab fa-python"></i>
                            Python Help
                        </button>
                        <button class="quick-action" data-prompt="Create a React component">
                            <i class="fab fa-react"></i>
                            React Code
                        </button>
                        <button class="quick-action" data-prompt="Debug my JavaScript code">
                            <i class="fab fa-js"></i>
                            Debug JS
                        </button>
                        <button class="quick-action" data-prompt="Explain this code">
                            <i class="fas fa-question-circle"></i>
                            Explain Code
                        </button>
                    </div>
                </div>
                
                <!-- Messages will be added here -->
            </div>
        </main>

        <!-- Mobile Input Area -->
        <div class="mobile-input-area">
            <form class="mobile-chat-form" id="mobileChatForm">
                <div class="input-container-mobile">
                    <textarea 
                        class="message-input-mobile" 
                        id="messageInputMobile" 
                        placeholder="Ask me anything..." 
                        rows="1"
                        maxlength="4000"
                    ></textarea>
                    <button type="submit" class="send-btn-mobile" id="sendBtnMobile">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- Mobile Loading -->
        <div class="mobile-loading" id="mobileLoading">
            <div class="loading-spinner-mobile">
                <div class="spinner"></div>
            </div>
            <p>Thinking...</p>
        </div>
    </div>

    <!-- Mobile Auth Modal -->
    <div id="authModalMobile" class="auth-modal-mobile">
        <div class="auth-content-mobile">
            <div class="auth-header-mobile">
                <h3>Sign In</h3>
                <button class="auth-close-mobile" id="authCloseMobile">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="auth-body-mobile">
                <!-- Auth content will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Mobile Template Debug -->
    <script>
        console.log('=== MOBILE TEMPLATE DEBUG ===');
        console.log('Mobile template loaded successfully');
        console.log('User Agent:', navigator.userAgent);
        console.log('Screen width:', window.innerWidth);
        console.log('Viewport height:', window.innerHeight);
        console.log('Document ready state:', document.readyState);
        console.log('Current URL:', window.location.href);

        // Check if elements exist
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');
            console.log('Mobile app element:', document.querySelector('.mobile-app'));
            console.log('Mobile header element:', document.querySelector('.mobile-header'));
            console.log('Body background:', getComputedStyle(document.body).backgroundColor);
            console.log('Body color:', getComputedStyle(document.body).color);

            // Force white background if needed
            document.body.style.backgroundColor = '#ffffff';
            document.body.style.color = '#000000';

            const mobileApp = document.querySelector('.mobile-app');
            if (mobileApp) {
                mobileApp.style.backgroundColor = '#ffffff';
                console.log('Mobile app container found and styled');
            } else {
                console.error('Mobile app container not found!');
            }
        });
        console.log('==============================');
    </script>

    <!-- Scripts with error handling -->
    <script>
        // Error handling for script loading
        window.addEventListener('error', function(e) {
            console.error('Script loading error:', e.filename, e.message);
        });

        // Basic functionality if scripts fail to load
        window.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking for mobile functionality...');

            // Basic mobile functionality fallback
            const menuBtn = document.getElementById('menuBtn');
            const sidebar = document.getElementById('mobileSidebar');
            const overlay = document.getElementById('mobileOverlay');

            if (menuBtn && sidebar && overlay) {
                menuBtn.addEventListener('click', function() {
                    sidebar.classList.add('active');
                    overlay.classList.add('active');
                });

                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                });

                const closeSidebar = document.getElementById('closeSidebar');
                if (closeSidebar) {
                    closeSidebar.addEventListener('click', function() {
                        sidebar.classList.remove('active');
                        overlay.classList.remove('active');
                    });
                }
            }

            // Basic form handling
            const chatForm = document.getElementById('mobileChatForm');
            const messageInput = document.getElementById('messageInputMobile');

            if (chatForm && messageInput) {
                chatForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const message = messageInput.value.trim();
                    if (message) {
                        console.log('Message submitted:', message);
                        // Basic message handling would go here
                        messageInput.value = '';
                    }
                });
            }
        });
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/9.1.6/marked.min.js" crossorigin="anonymous"></script>

    <!-- Load mobile scripts with fallback -->
    <script>
        function loadScript(src, callback) {
            const script = document.createElement('script');
            script.src = src;
            script.onload = callback;
            script.onerror = function() {
                console.error('Failed to load script:', src);
                if (callback) callback();
            };
            document.head.appendChild(script);
        }

        // Load mobile scripts
        loadScript('{{ url_for("static", filename="js/mobile-firebase.js") }}', function() {
            console.log('Mobile Firebase script loaded');
            loadScript('{{ url_for("static", filename="js/mobile.js") }}', function() {
                console.log('Mobile script loaded');
            });
        });
    </script>
</body>
</html>
